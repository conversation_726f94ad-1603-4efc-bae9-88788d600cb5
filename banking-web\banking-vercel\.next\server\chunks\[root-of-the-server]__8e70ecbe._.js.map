{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Code/Banking/banking-web/banking-vercel/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\n// Global cache to prevent multiple connections in serverless environment\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached!.conn) {\n    return cached!.conn;\n  }\n\n  if (!cached!.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n    };\n\n    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB Atlas');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached!.conn = await cached!.promise;\n  } catch (e) {\n    cached!.promise = null;\n    throw e;\n  }\n\n  return cached!.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAYA,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAQ,IAAI,EAAE;QAChB,OAAO,OAAQ,IAAI;IACrB;IAEA,IAAI,CAAC,OAAQ,OAAO,EAAE;QACpB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;QACnB;QAEA,OAAQ,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAQ,IAAI,GAAG,MAAM,OAAQ,OAAO;IACtC,EAAE,OAAO,GAAG;QACV,OAAQ,OAAO,GAAG;QAClB,MAAM;IACR;IAEA,OAAO,OAAQ,IAAI;AACrB;uCAEe", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Code/Banking/banking-web/banking-vercel/src/lib/models/Transaction.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\n// Schema cho dữ liệu từ MongoDB Atlas theo cấu trúc mới\nconst transactionSchema = new mongoose.Schema({\n  // Thông tin tài khoản\n  taiKhoanNhan: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  taiKhoanChuyen: {\n    type: String,\n    required: true,\n    trim: true,\n    index: true\n  },\n  tenNguoiChuyen: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  nganHangChuyen: {\n    type: String,\n    required: true,\n    trim: true,\n    index: true\n  },\n\n  // Thông tin giao dịch\n  loaiGiaoDich: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  maGiaoDich: {\n    type: String,\n    required: true,\n    unique: true,\n    index: true\n  },\n  ngayGioGiaoDich: {\n    type: Date,\n    required: true,\n    index: true\n  },\n\n  // Thông tin số tiền\n  soTien: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  soTienNumber: {\n    type: Number,\n    required: true,\n    index: true\n  },\n  phiGiaoDich: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  phiGiaoDichNumber: {\n    type: Number,\n    required: true,\n    default: 0\n  },\n\n  // Nội dung giao dịch\n  noiDungGiaoDich: {\n    type: String,\n    required: true,\n    trim: true\n  },\n\n  // Metadata\n  emailId: {\n    type: String,\n    required: true,\n    index: true\n  },\n  historyId: {\n    type: String,\n    required: true,\n    index: true\n  },\n  processedAt: {\n    type: Date,\n    required: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\ntransactionSchema.index({ ngayGioGiaoDich: -1 });\ntransactionSchema.index({ soTienNumber: 1, nganHangChuyen: 1 });\ntransactionSchema.index({ maGiaoDich: 1 });\ntransactionSchema.index({ taiKhoanChuyen: 1 });\n\nexport interface ITransaction extends mongoose.Document {\n  // Thông tin tài khoản\n  taiKhoanNhan: string;\n  taiKhoanChuyen: string;\n  tenNguoiChuyen: string;\n  nganHangChuyen: string;\n\n  // Thông tin giao dịch\n  loaiGiaoDich: string;\n  maGiaoDich: string;\n  ngayGioGiaoDich: Date;\n\n  // Thông tin số tiền\n  soTien: string;\n  soTienNumber: number;\n  phiGiaoDich: string;\n  phiGiaoDichNumber: number;\n\n  // Nội dung giao dịch\n  noiDungGiaoDich: string;\n\n  // Metadata\n  emailId: string;\n  historyId: string;\n  processedAt: Date;\n\n  // Timestamps\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport default mongoose.models.Transaction || mongoose.model<ITransaction>('Transaction', transactionSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,wDAAwD;AACxD,MAAM,oBAAoB,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IAC5C,sBAAsB;IACtB,cAAc;QACZ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;IACT;IACA,gBAAgB;QACd,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,gBAAgB;QACd,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;IACT;IAEA,sBAAsB;IACtB,cAAc;QACZ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;QACV,SAAS;IACX;IAEA,qBAAqB;IACrB,iBAAiB;QACf,MAAM;QACN,UAAU;QACV,MAAM;IACR;IAEA,WAAW;IACX,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;IACZ;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,kBAAkB,KAAK,CAAC;IAAE,iBAAiB,CAAC;AAAE;AAC9C,kBAAkB,KAAK,CAAC;IAAE,cAAc;IAAG,gBAAgB;AAAE;AAC7D,kBAAkB,KAAK,CAAC;IAAE,YAAY;AAAE;AACxC,kBAAkB,KAAK,CAAC;IAAE,gBAAgB;AAAE;uCAiC7B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,WAAW,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAe,eAAe", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Code/Banking/banking-web/banking-vercel/src/app/api/transactions/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport jwt from 'jsonwebtoken';\nimport connectDB from '@/lib/mongodb';\nimport Transaction from '@/lib/models/Transaction';\n\n// Middleware to verify JWT token\nasync function verifyToken(request: NextRequest) {\n  const authHeader = request.headers.get('authorization');\n  const token = authHeader?.replace('Bearer ', '');\n\n  if (!token) {\n    throw new Error('Token không được cung cấp');\n  }\n\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'banking-secret-key-vercel-2025');\n    return decoded;\n  } catch (error) {\n    throw new Error('Token không hợp lệ');\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Verify authentication\n    await verifyToken(request);\n\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n\n    // Get query parameters\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const search = searchParams.get('search');\n    const dateFrom = searchParams.get('dateFrom');\n    const dateTo = searchParams.get('dateTo');\n    const transactionType = searchParams.get('transactionType');\n    const minAmount = searchParams.get('minAmount');\n    const maxAmount = searchParams.get('maxAmount');\n    const senderBank = searchParams.get('senderBank');\n    const status = searchParams.get('status');\n\n    // Build filter object\n    const filter: any = {};\n\n    // Search across multiple fields\n    if (search) {\n      filter.$or = [\n        { tenNguoiChuyen: { $regex: search, $options: 'i' } },\n        { noiDungGiaoDich: { $regex: search, $options: 'i' } },\n        { maGiaoDich: { $regex: search, $options: 'i' } },\n        { taiKhoanChuyen: { $regex: search, $options: 'i' } },\n        { taiKhoanNhan: { $regex: search, $options: 'i' } }\n      ];\n    }\n\n    // Date range filter\n    if (dateFrom || dateTo) {\n      filter.ngayGioGiaoDich = {};\n      if (dateFrom) {\n        filter.ngayGioGiaoDich.$gte = new Date(dateFrom);\n      }\n      if (dateTo) {\n        const endDate = new Date(dateTo);\n        endDate.setHours(23, 59, 59, 999);\n        filter.ngayGioGiaoDich.$lte = endDate;\n      }\n    }\n\n    // Transaction type filter\n    if (transactionType) {\n      filter.loaiGiaoDich = { $regex: transactionType, $options: 'i' };\n    }\n\n    // Amount range filter\n    if (minAmount || maxAmount) {\n      filter.soTienNumber = {};\n      if (minAmount) {\n        filter.soTienNumber.$gte = parseFloat(minAmount);\n      }\n      if (maxAmount) {\n        filter.soTienNumber.$lte = parseFloat(maxAmount);\n      }\n    }\n\n    // Sender bank filter\n    if (senderBank) {\n      filter.nganHangChuyen = { $regex: senderBank, $options: 'i' };\n    }\n\n    // Calculate pagination\n    const skip = (page - 1) * limit;\n\n    // Get total count for pagination\n    const totalCount = await Transaction.countDocuments(filter);\n    const totalPages = Math.ceil(totalCount / limit);\n\n    // Get transactions\n    const transactions = await Transaction.find(filter)\n      .sort({ ngayGioGiaoDich: -1 })\n      .skip(skip)\n      .limit(limit)\n      .lean();\n\n    return NextResponse.json({\n      success: true,\n      transactions,\n      pagination: {\n        currentPage: page,\n        totalPages,\n        totalCount,\n        limit,\n        hasNext: page < totalPages,\n        hasPrev: page > 1\n      }\n    });\n\n  } catch (error: any) {\n    console.error('Get transactions error:', error);\n\n    if (error.message.includes('Token')) {\n      return NextResponse.json({\n        success: false,\n        message: error.message\n      }, { status: 401 });\n    }\n\n    return NextResponse.json({\n      success: false,\n      message: 'Lỗi khi tải danh sách giao dịch'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,iCAAiC;AACjC,eAAe,YAAY,OAAoB;IAC7C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,MAAM,QAAQ,YAAY,QAAQ,WAAW;IAE7C,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,wBAAwB;QACxB,MAAM,YAAY;QAElB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,uBAAuB;QACvB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,kBAAkB,aAAa,GAAG,CAAC;QACzC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,sBAAsB;QACtB,MAAM,SAAc,CAAC;QAErB,gCAAgC;QAChC,IAAI,QAAQ;YACV,OAAO,GAAG,GAAG;gBACX;oBAAE,gBAAgB;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACpD;oBAAE,iBAAiB;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACrD;oBAAE,YAAY;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAChD;oBAAE,gBAAgB;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACpD;oBAAE,cAAc;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;aACnD;QACH;QAEA,oBAAoB;QACpB,IAAI,YAAY,QAAQ;YACtB,OAAO,eAAe,GAAG,CAAC;YAC1B,IAAI,UAAU;gBACZ,OAAO,eAAe,CAAC,IAAI,GAAG,IAAI,KAAK;YACzC;YACA,IAAI,QAAQ;gBACV,MAAM,UAAU,IAAI,KAAK;gBACzB,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;gBAC7B,OAAO,eAAe,CAAC,IAAI,GAAG;YAChC;QACF;QAEA,0BAA0B;QAC1B,IAAI,iBAAiB;YACnB,OAAO,YAAY,GAAG;gBAAE,QAAQ;gBAAiB,UAAU;YAAI;QACjE;QAEA,sBAAsB;QACtB,IAAI,aAAa,WAAW;YAC1B,OAAO,YAAY,GAAG,CAAC;YACvB,IAAI,WAAW;gBACb,OAAO,YAAY,CAAC,IAAI,GAAG,WAAW;YACxC;YACA,IAAI,WAAW;gBACb,OAAO,YAAY,CAAC,IAAI,GAAG,WAAW;YACxC;QACF;QAEA,qBAAqB;QACrB,IAAI,YAAY;YACd,OAAO,cAAc,GAAG;gBAAE,QAAQ;gBAAY,UAAU;YAAI;QAC9D;QAEA,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,iCAAiC;QACjC,MAAM,aAAa,MAAM,qIAAA,CAAA,UAAW,CAAC,cAAc,CAAC;QACpD,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;QAE1C,mBAAmB;QACnB,MAAM,eAAe,MAAM,qIAAA,CAAA,UAAW,CAAC,IAAI,CAAC,QACzC,IAAI,CAAC;YAAE,iBAAiB,CAAC;QAAE,GAC3B,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;QAEP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,YAAY;gBACV,aAAa;gBACb;gBACA;gBACA;gBACA,SAAS,OAAO;gBAChB,SAAS,OAAO;YAClB;QACF;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS,MAAM,OAAO;YACxB,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}