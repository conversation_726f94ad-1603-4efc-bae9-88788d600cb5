{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Code/Banking/banking-web/banking-vercel/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { LogOut, CreditCard, RefreshCw, Search, Calendar, X, Filter } from 'lucide-react';\n\ninterface DashboardProps {\n  onLogout: () => void;\n}\n\ninterface Transaction {\n  _id: string;\n  // Thông tin tài khoản\n  taiKhoanNhan: string;\n  taiKhoanChuyen: string;\n  tenNguoiChuyen: string;\n  nganHangChuyen: string;\n\n  // Thông tin giao dịch\n  loaiGiaoDich: string;\n  maGiaoDich: string;\n  ngayGioGiaoDich: string;\n\n  // Thông tin số tiền\n  soTien: string;\n  soTienNumber: number;\n  phiGiaoDich: string;\n  phiGiaoDichNumber: number;\n\n  // Nội dung giao dịch\n  noiDungGiaoDich: string;\n\n  // Metadata\n  emailId: string;\n  historyId: string;\n  processedAt: string;\n\n  // Timestamps\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface Filters {\n  search: string;\n}\n\nexport default function Dashboard({ onLogout }: DashboardProps) {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);\n  const [filters, setFilters] = useState<Filters>({\n    search: ''\n  });\n  const [autoRefresh, setAutoRefresh] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  // Auto refresh effect\n  useEffect(() => {\n    if (!autoRefresh) return;\n\n    const interval = setInterval(() => {\n      loadData();\n    }, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, [autoRefresh]);\n\n  useEffect(() => {\n    applyFilters();\n  }, [transactions, filters]);\n\n  const loadData = async (showLoading = true) => {\n    try {\n      if (showLoading) {\n        setLoading(true);\n      }\n      setError('');\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        onLogout();\n        return;\n      }\n\n      const headers = {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      };\n\n      // Load all transactions (no limit for filtering)\n      const transactionsResponse = await fetch('/api/transactions?limit=1000', {\n        headers\n      });\n\n      if (transactionsResponse.ok) {\n        const transactionsData = await transactionsResponse.json();\n        setTransactions(transactionsData.transactions || []);\n        setLastUpdated(new Date());\n      } else {\n        setError('Không thể tải dữ liệu');\n      }\n    } catch (error) {\n      console.error('Load data error:', error);\n      setError('Lỗi kết nối');\n    } finally {\n      if (showLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  const applyFilters = () => {\n    let filtered = [...transactions];\n\n    // Search filter\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filtered = filtered.filter(transaction =>\n        transaction.tenNguoiChuyen?.toLowerCase().includes(searchLower) ||\n        transaction.taiKhoanChuyen?.toLowerCase().includes(searchLower) ||\n        transaction.taiKhoanNhan?.toLowerCase().includes(searchLower) ||\n        transaction.noiDungGiaoDich?.toLowerCase().includes(searchLower) ||\n        transaction.maGiaoDich?.toLowerCase().includes(searchLower) ||\n        transaction.soTienNumber.toString().includes(searchLower) ||\n        transaction.nganHangChuyen?.toLowerCase().includes(searchLower) ||\n        transaction.loaiGiaoDich?.toLowerCase().includes(searchLower)\n      );\n    }\n\n\n\n\n\n    // Sort by date (newest first)\n    filtered.sort((a, b) => new Date(b.ngayGioGiaoDich).getTime() - new Date(a.ngayGioGiaoDich).getTime());\n\n    setFilteredTransactions(filtered);\n  };\n\n  const handleFilterChange = (key: keyof Filters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND'\n    }).format(Math.abs(amount));\n  };\n\n  const formatDate = (date?: string) => {\n    if (!date) return 'N/A';\n    return new Date(date).toLocaleString('vi-VN');\n  };\n\n  const formatDateInput = (date: string) => {\n    return new Date(date).toISOString().slice(0, 16);\n  };\n\n  const handleLogout = () => {\n    if (window.confirm('Bạn có chắc chắn muốn đăng xuất?')) {\n      onLogout();\n    }\n  };\n\n  const openTransactionDetail = (transaction: Transaction) => {\n    setSelectedTransaction(transaction);\n  };\n\n  const closeTransactionDetail = () => {\n    setSelectedTransaction(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white p-8 rounded-2xl shadow-lg\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Đang tải dữ liệu</h3>\n          <p className=\"text-gray-600\">Vui lòng chờ trong giây lát...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <CreditCard className=\"h-5 w-5 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">Banking</h1>\n                <p className=\"text-xs text-gray-500 hidden sm:block\">Transaction Management</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4\">\n\n\n              <button\n                onClick={loadData}\n                disabled={loading}\n                className=\"px-3 py-2 bg-gray-200 text-gray-900 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 flex items-center space-x-2\"\n              >\n                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\n                <span className=\"hidden sm:inline\">Làm mới</span>\n              </button>\n\n              <button\n                onClick={handleLogout}\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100\"\n              >\n                <LogOut className=\"h-4 w-4 sm:h-5 sm:w-5\" />\n                <span className=\"hidden sm:inline text-sm font-medium\">Đăng xuất</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\">\n        <div className=\"space-y-4 sm:space-y-6\">\n          {/* Page Title */}\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">Giao dịch ngân hàng</h1>\n            <p className=\"text-gray-600 text-sm sm:text-base max-w-2xl mx-auto\">\n              Quản lý và theo dõi các giao dịch từ MongoDB Atlas\n            </p>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6\">\n            <div className=\"max-w-md mx-auto\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-3 text-center\">\n                Tìm kiếm giao dịch\n              </label>\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Tìm theo tên, nội dung, mã giao dịch, số tiền...\"\n                  className=\"w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm\"\n                  value={filters.search}\n                  onChange={(e) => handleFilterChange('search', e.target.value)}\n                />\n              </div>\n              {filters.search && (\n                <div className=\"mt-2 text-center\">\n                  <button\n                    onClick={() => handleFilterChange('search', '')}\n                    className=\"text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200\"\n                  >\n                    Xóa tìm kiếm\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n              <p className=\"text-red-600\">{error}</p>\n            </div>\n          )}\n\n          {/* Transactions List */}\n          <div className=\"bg-white rounded-lg shadow-lg border border-gray-200\">\n            <div className=\"p-4 sm:p-6 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg sm:text-xl font-semibold text-gray-900 flex items-center\">\n                  <CreditCard className=\"h-5 w-5 sm:h-6 sm:w-6 mr-2 text-blue-600\" />\n                  Danh sách giao dịch\n                </h2>\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-500\">\n                    {filteredTransactions.length} giao dịch\n                  </span>\n                  <button\n                    onClick={loadData}\n                    disabled={loading}\n                    className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200 disabled:opacity-50\"\n                    title=\"Làm mới\"\n                  >\n                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"divide-y divide-gray-200 max-h-[500px] overflow-y-auto\">\n              {filteredTransactions.length > 0 ? (\n                filteredTransactions.map((transaction) => (\n                  <div\n                    key={transaction._id}\n                    className=\"p-4 sm:p-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 cursor-pointer transition-all duration-200 border-l-4 border-transparent hover:border-blue-400\"\n                    onClick={() => openTransactionDetail(transaction)}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1 min-w-0 pr-4\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <div className={`w-3 h-3 rounded-full ${transaction.soTienNumber > 0 ? 'bg-green-400' : 'bg-red-400'}`}></div>\n                          <p className=\"text-sm font-semibold text-gray-900 truncate\">\n                            {transaction.tenNguoiChuyen}\n                          </p>\n                          <span className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\">\n                            {transaction.nganHangChuyen}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-700 truncate mb-2\">\n                          {transaction.noiDungGiaoDich || 'Không có mô tả'}\n                        </p>\n                        <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                          <span>{formatDate(transaction.ngayGioGiaoDich)}</span>\n                          <span className=\"text-blue-600 font-medium\">\n                            {transaction.loaiGiaoDich}\n                          </span>\n                        </div>\n                        <p className=\"text-xs text-gray-400 mt-1\">\n                          Mã GD: {transaction.maGiaoDich}\n                        </p>\n                      </div>\n                      <div className=\"text-right flex-shrink-0\">\n                        <p className={`text-lg sm:text-xl font-bold ${transaction.soTienNumber > 0\n                          ? 'text-green-600'\n                          : 'text-red-600'\n                          }`}>\n                          {transaction.soTienNumber > 0 ? '+' : ''}\n                          {formatCurrency(transaction.soTienNumber)}\n                        </p>\n                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mt-2 ${transaction.soTienNumber > 0\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-red-100 text-red-800'\n                          }`}>\n                          {transaction.soTienNumber > 0 ? '↗️ Tiền vào' : '↙️ Tiền ra'}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"p-12 text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\">\n                    <CreditCard className=\"h-8 w-8 text-gray-400\" />\n                  </div>\n                  <p className=\"text-gray-500 text-lg mb-2\">\n                    {filters.search\n                      ? 'Không tìm thấy giao dịch nào phù hợp'\n                      : 'Chưa có giao dịch nào'\n                    }\n                  </p>\n                  {filters.search && (\n                    <p className=\"text-gray-400 text-sm\">\n                      Thử tìm kiếm với từ khóa khác\n                    </p>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Transaction Detail Modal */}\n      {selectedTransaction && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-4 sm:p-6\">\n              {/* Header */}\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900\">\n                  Chi tiết giao dịch\n                </h3>\n                <button\n                  onClick={closeTransactionDetail}\n                  className=\"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200\"\n                >\n                  <X className=\"h-5 w-5 text-gray-500\" />\n                </button>\n              </div>\n\n              {/* Transaction Info */}\n              <div className=\"space-y-4\">\n                {/* Amount */}\n                <div className=\"text-center py-4 border-b border-gray-200\">\n                  <p className={`text-2xl sm:text-3xl font-bold ${selectedTransaction.soTienNumber > 0\n                    ? 'text-green-600'\n                    : 'text-red-600'\n                    }`}>\n                    {selectedTransaction.soTienNumber > 0 ? '+' : ''}\n                    {formatCurrency(selectedTransaction.soTienNumber)}\n                  </p>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    VND\n                  </p>\n                  <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${selectedTransaction.soTienNumber > 0\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-red-100 text-red-800'\n                    }`}>\n                    {selectedTransaction.soTienNumber > 0 ? 'Tiền vào' : 'Tiền ra'}\n                  </div>\n                </div>\n\n                {/* Details Grid */}\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Mã giao dịch\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.maGiaoDich}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Thời gian\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {formatDate(selectedTransaction.ngayGioGiaoDich)}\n                    </p>\n                  </div>\n\n                  <div className=\"sm:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Loại giao dịch\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-blue-50 p-2 rounded font-medium\">\n                      {selectedTransaction.loaiGiaoDich}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tên người chuyển\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.tenNguoiChuyen}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tài khoản chuyển\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.taiKhoanChuyen}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Ngân hàng chuyển\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.nganHangChuyen}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tài khoản nhận\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.taiKhoanNhan}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Phí giao dịch\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.phiGiaoDich}\n                    </p>\n                  </div>\n                </div>\n\n                {/* Description */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Nội dung chuyển khoản\n                  </label>\n                  <p className=\"text-sm text-gray-900 bg-gray-50 p-3 rounded\">\n                    {selectedTransaction.noiDungGiaoDich}\n                  </p>\n                </div>\n\n                {/* Reference */}\n                {selectedTransaction.reference && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tham chiếu\n                    </label>\n                    <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded\">\n                      {selectedTransaction.reference}\n                    </p>\n                  </div>\n                )}\n              </div>\n\n              {/* Close Button */}\n              <div className=\"mt-6 flex justify-end\">\n                <button\n                  onClick={closeTransactionDetail}\n                  className=\"px-4 py-2 bg-gray-200 text-gray-900 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200\"\n                >\n                  Đóng\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA6Ce,SAAS,UAAU,EAAE,QAAQ,EAAkB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;QAC9C,QAAQ;IACV;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;QAElB,MAAM,WAAW,YAAY;YAC3B;QACF,GAAG,QAAQ,2BAA2B;QAEtC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;KAAQ;IAE1B,MAAM,WAAW,OAAO,cAAc,IAAI;QACxC,IAAI;YACF,IAAI,aAAa;gBACf,WAAW;YACb;YACA,SAAS;YAET,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV;gBACA;YACF;YAEA,MAAM,UAAU;gBACd,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,gBAAgB;YAClB;YAEA,iDAAiD;YACjD,MAAM,uBAAuB,MAAM,MAAM,gCAAgC;gBACvE;YACF;YAEA,IAAI,qBAAqB,EAAE,EAAE;gBAC3B,MAAM,mBAAmB,MAAM,qBAAqB,IAAI;gBACxD,gBAAgB,iBAAiB,YAAY,IAAI,EAAE;gBACnD,eAAe,IAAI;YACrB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,SAAS;QACX,SAAU;YACR,IAAI,aAAa;gBACf,WAAW;YACb;QACF;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;eAAI;SAAa;QAEhC,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,WAAW,SAAS,MAAM,CAAC,CAAA,cACzB,YAAY,cAAc,EAAE,cAAc,SAAS,gBACnD,YAAY,cAAc,EAAE,cAAc,SAAS,gBACnD,YAAY,YAAY,EAAE,cAAc,SAAS,gBACjD,YAAY,eAAe,EAAE,cAAc,SAAS,gBACpD,YAAY,UAAU,EAAE,cAAc,SAAS,gBAC/C,YAAY,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC,gBAC7C,YAAY,cAAc,EAAE,cAAc,SAAS,gBACnD,YAAY,YAAY,EAAE,cAAc,SAAS;QAErD;QAMA,8BAA8B;QAC9B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,eAAe,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,eAAe,EAAE,OAAO;QAEnG,wBAAwB;IAC1B;IAEA,MAAM,qBAAqB,CAAC,KAAoB;QAC9C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAIA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC;IACrB;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,IAAI,KAAK,MAAM,cAAc,CAAC;IACvC;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG;IAC/C;IAEA,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,qCAAqC;YACtD;QACF;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,uBAAuB;IACzB;IAEA,MAAM,yBAAyB;QAC7B,uBAAuB;IACzB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDAGb,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;0DAChE,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAGrC,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAClE,8OAAC;oCAAE,WAAU;8CAAuD;;;;;;;;;;;;sCAMtE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA2D;;;;;;kDAG5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;gDACV,OAAO,QAAQ,MAAM;gDACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oCAG/D,QAAQ,MAAM,kBACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,mBAAmB,UAAU;4CAC5C,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;wBASR,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;sCAKjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAA6C;;;;;;;0DAGrE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,qBAAqB,MAAM;4DAAC;;;;;;;kEAE/B,8OAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMxE,8OAAC;oCAAI,WAAU;8CACZ,qBAAqB,MAAM,GAAG,IAC7B,qBAAqB,GAAG,CAAC,CAAC,4BACxB,8OAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,YAAY,GAAG,IAAI,iBAAiB,cAAc;;;;;;kFACtG,8OAAC;wEAAE,WAAU;kFACV,YAAY,cAAc;;;;;;kFAE7B,8OAAC;wEAAK,WAAU;kFACb,YAAY,cAAc;;;;;;;;;;;;0EAG/B,8OAAC;gEAAE,WAAU;0EACV,YAAY,eAAe,IAAI;;;;;;0EAElC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAM,WAAW,YAAY,eAAe;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;kFACb,YAAY,YAAY;;;;;;;;;;;;0EAG7B,8OAAC;gEAAE,WAAU;;oEAA6B;oEAChC,YAAY,UAAU;;;;;;;;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAW,CAAC,6BAA6B,EAAE,YAAY,YAAY,GAAG,IACrE,mBACA,gBACA;;oEACD,YAAY,YAAY,GAAG,IAAI,MAAM;oEACrC,eAAe,YAAY,YAAY;;;;;;;0EAE1C,8OAAC;gEAAI,WAAW,CAAC,yEAAyE,EAAE,YAAY,YAAY,GAAG,IACnH,gCACA,2BACA;0EACD,YAAY,YAAY,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;2CAxCjD,YAAY,GAAG;;;;kEA+CxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,MAAM,GACX,yCACA;;;;;;4CAGL,QAAQ,MAAM,kBACb,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYlD,qCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAW,CAAC,+BAA+B,EAAE,oBAAoB,YAAY,GAAG,IAC/E,mBACA,gBACA;;oDACD,oBAAoB,YAAY,GAAG,IAAI,MAAM;oDAC7C,eAAe,oBAAoB,YAAY;;;;;;;0DAElD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC;gDAAI,WAAW,CAAC,6DAA6D,EAAE,oBAAoB,YAAY,GAAG,IAC/G,gCACA,2BACA;0DACD,oBAAoB,YAAY,GAAG,IAAI,aAAa;;;;;;;;;;;;kDAKzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,UAAU;;;;;;;;;;;;0DAInC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,WAAW,oBAAoB,eAAe;;;;;;;;;;;;0DAInD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,YAAY;;;;;;;;;;;;0DAIrC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,cAAc;;;;;;;;;;;;0DAIvC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,cAAc;;;;;;;;;;;;0DAIvC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,cAAc;;;;;;;;;;;;0DAIvC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,YAAY;;;;;;;;;;;;0DAIrC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,WAAW;;;;;;;;;;;;;;;;;;kDAMtC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAE,WAAU;0DACV,oBAAoB,eAAe;;;;;;;;;;;;oCAKvC,oBAAoB,SAAS,kBAC5B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAE,WAAU;0DACV,oBAAoB,SAAS;;;;;;;;;;;;;;;;;;0CAOtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Code/Banking/banking-web/banking-vercel/src/components/Login.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Eye, EyeOff, Lock, User, AlertCircle } from 'lucide-react';\n\ninterface LoginProps {\n  onLogin: () => void;\n}\n\nexport default function Login({ onLogin }: LoginProps) {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        localStorage.setItem('token', data.token);\n        onLogin();\n      } else {\n        setError(data.message);\n      }\n    } catch (error) {\n      setError('Lỗi kết nối. Vui lòng thử lại.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100 px-4\" style={{ background: 'linear-gradient(to bottom right, #eff6ff, #dbeafe)' }}>\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center\">\n            <Lock className=\"h-8 w-8 text-white\" />\n          </div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Đăng nhập\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Truy cập hệ thống quản lý giao dịch ngân hàng\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Tên đăng nhập\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <User className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  required\n                  className=\"w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"Nhập tên đăng nhập\"\n                  value={formData.username}\n                  onChange={handleChange}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Mật khẩu\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  className=\"w-full px-3 py-2 pl-10 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"Nhập mật khẩu\"\n                  value={formData.password}\n                  onChange={handleChange}\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                  ) : (\n                    <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg\">\n              <AlertCircle className=\"h-5 w-5\" />\n              <span className=\"text-sm\">{error}</span>\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full px-4 py-3 bg-blue-600 text-white text-base font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n          >\n            {loading ? (\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                <span>Đang đăng nhập...</span>\n              </div>\n            ) : (\n              'Đăng nhập'\n            )}\n          </button>\n        </form>\n\n        <div className=\"text-center\">\n          <p className=\"text-xs text-gray-500\">\n            Banking Transactions v2.0 - Vercel Edition\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AASe,SAAS,MAAM,EAAE,OAAO,EAAc;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;QACA,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;gBACxC;YACF,OAAO;gBACL,SAAS,KAAK,OAAO;YACvB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAgG,OAAO;YAAE,YAAY;QAAqD;kBACvL,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOxB,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;sCAI/B,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;uCAGR;;;;;;;;;;;;8BAKN,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Code/Banking/banking-web/banking-vercel/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport Dashboard from '@/components/Dashboard';\nimport Login from '@/components/Login';\n\nexport default function Home() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      try {\n        const response = await fetch('/api/auth/verify', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        \n        if (response.ok) {\n          setIsAuthenticated(true);\n        } else {\n          localStorage.removeItem('token');\n        }\n      } catch (error) {\n        localStorage.removeItem('token');\n      }\n    }\n    setLoading(false);\n  };\n\n  const handleLogin = () => {\n    setIsAuthenticated(true);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    setIsAuthenticated(false);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return <Login onLogin={handleLogin} />;\n  }\n\n  return <Dashboard onLogout={handleLogout} />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;oBAC/C,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,mBAAmB;gBACrB,OAAO;oBACL,aAAa,UAAU,CAAC;gBAC1B;YACF,EAAE,OAAO,OAAO;gBACd,aAAa,UAAU,CAAC;YAC1B;QACF;QACA,WAAW;IACb;IAEA,MAAM,cAAc;QAClB,mBAAmB;IACrB;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,mBAAmB;IACrB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBAAO,8OAAC,2HAAA,CAAA,UAAK;YAAC,SAAS;;;;;;IACzB;IAEA,qBAAO,8OAAC,+HAAA,CAAA,UAAS;QAAC,UAAU;;;;;;AAC9B", "debugId": null}}]}