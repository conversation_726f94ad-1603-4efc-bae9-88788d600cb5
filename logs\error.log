{"level":"error","message":"Failed to parse email test-message-id: this.patterns.transactionTime is not iterable","service":"banking-backend","stack":"TypeError: this.patterns.transactionTime is not iterable\n    at EmailParser.extractTransactionTime (D:\\Code\\Banking\\src\\services\\emailParser.js:221:41)\n    at EmailParser.parseTransactionEmail (D:\\Code\\Banking\\src\\services\\emailParser.js:68:31)\n    at EmailParser.testParsing (D:\\Code\\Banking\\src\\services\\emailParser.js:354:17)\n    at testEmailParser (D:\\Code\\Banking\\test-setup.js:40:38)\n    at runAllTests (D:\\Code\\Banking\\test-setup.js:165:9)","timestamp":"2025-08-05 13:24:21"}
{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"currentRetryAttempt":0,"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"],"maxRetryDelay":****************,"noResponseRetries":2,"retry":3,"retryDelayMultiplier":2,"statusCodesToRetry":[[100,199],[408,408],[429,429],[500,599]],"timeOfFirstRequest":*************,"totalTimeout":****************},"url":"https://oauth2.googleapis.com/token"},"level":"error","message":"Gmail connection test failed: invalid_grant","response":{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"]},"url":"https://oauth2.googleapis.com/token"},"data":{"error":"invalid_grant","error_description":"Bad Request"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","cache-control":"no-cache, no-store, max-age=0, must-revalidate","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Tue, 05 Aug 2025 06:38:44 GMT","expires":"Mon, 01 Jan 1990 00:00:00 GMT","pragma":"no-cache","server":"scaffolding on HTTPServer2","transfer-encoding":"chunked","vary":"Origin, X-Origin, Referer","x-content-type-options":"nosniff","x-frame-options":"SAMEORIGIN","x-xss-protection":"0"},"request":{"responseURL":"https://oauth2.googleapis.com/token"},"status":400,"statusText":"Bad Request"},"service":"banking-backend","stack":"Error: invalid_grant\n    at Gaxios._request (D:\\Code\\Banking\\node_modules\\gaxios\\build\\src\\gaxios.js:142:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async OAuth2Client.refreshTokenNoCache (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:212:19)\n    at async OAuth2Client.getRequestMetadataAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:333:17)\n    at async OAuth2Client.requestAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:418:23)\n    at async GmailService.testConnection (D:\\Code\\Banking\\src\\services\\gmailService.js:114:23)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:39:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:117:5)","status":400,"timestamp":"2025-08-05 13:38:45"}
{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"currentRetryAttempt":0,"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"],"maxRetryDelay":****************,"noResponseRetries":2,"retry":3,"retryDelayMultiplier":2,"statusCodesToRetry":[[100,199],[408,408],[429,429],[500,599]],"timeOfFirstRequest":*************,"totalTimeout":****************},"url":"https://oauth2.googleapis.com/token"},"level":"error","message":"Gmail connection test failed: invalid_grant","response":{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"]},"url":"https://oauth2.googleapis.com/token"},"data":{"error":"invalid_grant","error_description":"Bad Request"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","cache-control":"no-cache, no-store, max-age=0, must-revalidate","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Tue, 05 Aug 2025 06:39:13 GMT","expires":"Mon, 01 Jan 1990 00:00:00 GMT","pragma":"no-cache","server":"scaffolding on HTTPServer2","transfer-encoding":"chunked","vary":"Origin, X-Origin, Referer","x-content-type-options":"nosniff","x-frame-options":"SAMEORIGIN","x-xss-protection":"0"},"request":{"responseURL":"https://oauth2.googleapis.com/token"},"status":400,"statusText":"Bad Request"},"service":"banking-backend","stack":"Error: invalid_grant\n    at Gaxios._request (D:\\Code\\Banking\\node_modules\\gaxios\\build\\src\\gaxios.js:142:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async OAuth2Client.refreshTokenNoCache (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:212:19)\n    at async OAuth2Client.getRequestMetadataAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:333:17)\n    at async OAuth2Client.requestAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:418:23)\n    at async GmailService.testConnection (D:\\Code\\Banking\\src\\services\\gmailService.js:114:23)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:39:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:118:7)","status":400,"timestamp":"2025-08-05 13:39:13"}
{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"currentRetryAttempt":0,"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"],"maxRetryDelay":****************,"noResponseRetries":2,"retry":3,"retryDelayMultiplier":2,"statusCodesToRetry":[[100,199],[408,408],[429,429],[500,599]],"timeOfFirstRequest":*************,"totalTimeout":****************},"url":"https://oauth2.googleapis.com/token"},"level":"error","message":"Failed to initialize Gmail service: invalid_grant","response":{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"]},"url":"https://oauth2.googleapis.com/token"},"data":{"error":"invalid_grant","error_description":"Bad Request"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","cache-control":"no-cache, no-store, max-age=0, must-revalidate","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Tue, 05 Aug 2025 06:39:13 GMT","expires":"Mon, 01 Jan 1990 00:00:00 GMT","pragma":"no-cache","server":"scaffolding on HTTPServer2","transfer-encoding":"chunked","vary":"Origin, X-Origin, Referer","x-content-type-options":"nosniff","x-frame-options":"SAMEORIGIN","x-xss-protection":"0"},"request":{"responseURL":"https://oauth2.googleapis.com/token"},"status":400,"statusText":"Bad Request"},"service":"banking-backend","stack":"Error: invalid_grant\n    at Gaxios._request (D:\\Code\\Banking\\node_modules\\gaxios\\build\\src\\gaxios.js:142:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async OAuth2Client.refreshTokenNoCache (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:212:19)\n    at async OAuth2Client.getRequestMetadataAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:333:17)\n    at async OAuth2Client.requestAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:418:23)\n    at async GmailService.testConnection (D:\\Code\\Banking\\src\\services\\gmailService.js:114:23)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:39:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:118:7)","status":400,"timestamp":"2025-08-05 13:39:13"}
{"level":"error","message":"Failed to initialize Gmail service: Gmail tokens not found. Please run the authorization flow first","service":"banking-backend","stack":"Error: Gmail tokens not found. Please run the authorization flow first\n    at GmailService.loadTokens (D:\\Code\\Banking\\src\\services\\gmailService.js:93:13)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:33:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:118:7)","timestamp":"2025-08-05 13:39:29"}
{"body":"{\" code\\: \\4/0AVMBsJhIztJBh9J3N9iMLUylSLSmIq0ZaO1PCm1bcjbQ6Dg6bpf5DY89oJ1NqvWFZzl2Fw\\}","expose":true,"level":"error","message":"Unhandled error: Bad escaped character in JSON at position 8 (line 1 column 9)","service":"banking-backend","stack":"SyntaxError: Bad escaped character in JSON at position 8 (line 1 column 9)\n    at JSON.parse (<anonymous>)\n    at parse (D:\\Code\\Banking\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at D:\\Code\\Banking\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (D:\\Code\\Banking\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Code\\Banking\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Code\\Banking\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:524:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-08-05 13:47:19","type":"entity.parse.failed"}
{"level":"error","message":"Failed to initialize Gmail service: Gmail tokens not found. Please run the authorization flow first","service":"banking-backend","stack":"Error: Gmail tokens not found. Please run the authorization flow first\n    at GmailService.loadTokens (D:\\Code\\Banking\\src\\services\\gmailService.js:93:13)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:33:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:118:7)","timestamp":"2025-08-05 13:47:48"}
{"level":"error","message":"Failed to parse email 198790b72b4ab848: this.patterns.senderName is not iterable","service":"banking-backend","stack":"TypeError: this.patterns.senderName is not iterable\n    at EmailParser.extractSenderName (D:\\Code\\Banking\\src\\services\\emailParser.js:160:41)\n    at EmailParser.parseTransactionEmail (D:\\Code\\Banking\\src\\services\\emailParser.js:72:26)\n    at GmailService.processMessage (D:\\Code\\Banking\\src\\services\\gmailService.js:224:49)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GmailService.checkNewEmails (D:\\Code\\Banking\\src\\services\\gmailService.js:179:11)\n    at async cron.schedule.scheduled [as _execution] (D:\\Code\\Banking\\src\\server.js:80:7)","timestamp":"2025-08-05 14:06:31"}
{"level":"error","message":"Failed to parse email 198790694f976676: this.patterns.senderName is not iterable","service":"banking-backend","stack":"TypeError: this.patterns.senderName is not iterable\n    at EmailParser.extractSenderName (D:\\Code\\Banking\\src\\services\\emailParser.js:160:41)\n    at EmailParser.parseTransactionEmail (D:\\Code\\Banking\\src\\services\\emailParser.js:72:26)\n    at GmailService.processMessage (D:\\Code\\Banking\\src\\services\\gmailService.js:224:49)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GmailService.checkNewEmails (D:\\Code\\Banking\\src\\services\\gmailService.js:179:11)\n    at async cron.schedule.scheduled [as _execution] (D:\\Code\\Banking\\src\\server.js:80:7)","timestamp":"2025-08-05 14:06:31"}
{"level":"error","message":"Failed to parse email 18829ad3a370248d: this.patterns.senderName is not iterable","service":"banking-backend","stack":"TypeError: this.patterns.senderName is not iterable\n    at EmailParser.extractSenderName (D:\\Code\\Banking\\src\\services\\emailParser.js:160:41)\n    at EmailParser.parseTransactionEmail (D:\\Code\\Banking\\src\\services\\emailParser.js:72:26)\n    at GmailService.processMessage (D:\\Code\\Banking\\src\\services\\gmailService.js:224:49)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GmailService.checkNewEmails (D:\\Code\\Banking\\src\\services\\gmailService.js:179:11)\n    at async cron.schedule.scheduled [as _execution] (D:\\Code\\Banking\\src\\server.js:80:7)","timestamp":"2025-08-05 14:06:32"}
{"level":"error","message":"Parser test failed: EmailParser is not a constructor","service":"banking-backend","stack":"TypeError: EmailParser is not a constructor\n    at D:\\Code\\Banking\\src\\routes\\gmail-auth.js:48:25\n    at Layer.handle [as handle_request] (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-08-05 15:15:56"}
{"level":"error","message":"Parser test failed: EmailParser is not a constructor","service":"banking-backend","stack":"TypeError: EmailParser is not a constructor\n    at D:\\Code\\Banking\\src\\routes\\gmail-auth.js:48:25\n    at Layer.handle [as handle_request] (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\Code\\Banking\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-08-05 15:22:31"}
{"_message":"Transaction validation failed","errors":{"status":{"kind":"enum","message":"`SKIPPED` is not a valid enum value for path `status`.","name":"ValidatorError","path":"status","properties":{"enumValues":["PENDING","PROCESSED","ERROR","DUPLICATE"],"message":"`SKIPPED` is not a valid enum value for path `status`.","path":"status","type":"enum","value":"SKIPPED"},"value":"SKIPPED"}},"level":"error","message":"Failed to process message 1987962be098f52e: Transaction validation failed: status: `SKIPPED` is not a valid enum value for path `status`.","service":"banking-backend","stack":"ValidationError: Transaction validation failed: status: `SKIPPED` is not a valid enum value for path `status`.\n    at Document.invalidate (D:\\Code\\Banking\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\Code\\Banking\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\Code\\Banking\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-08-05 15:39:41"}
{"level":"error","message":"Failed to save error transaction: body is not defined","service":"banking-backend","stack":"ReferenceError: body is not defined\n    at GmailService.processMessage (D:\\Code\\Banking\\src\\services\\gmailService.js:308:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GmailService.checkNewEmails (D:\\Code\\Banking\\src\\services\\gmailService.js:186:11)\n    at async cron.schedule.scheduled [as _execution] (D:\\Code\\Banking\\src\\server.js:80:7)","timestamp":"2025-08-05 15:39:41"}
{"_message":"Transaction validation failed","errors":{"status":{"kind":"enum","message":"`SKIPPED` is not a valid enum value for path `status`.","name":"ValidatorError","path":"status","properties":{"enumValues":["PENDING","PROCESSED","ERROR","DUPLICATE"],"message":"`SKIPPED` is not a valid enum value for path `status`.","path":"status","type":"enum","value":"SKIPPED"},"value":"SKIPPED"}},"level":"error","message":"Failed to process message 1987962be098f52e: Transaction validation failed: status: `SKIPPED` is not a valid enum value for path `status`.","service":"banking-backend","stack":"ValidationError: Transaction validation failed: status: `SKIPPED` is not a valid enum value for path `status`.\n    at Document.invalidate (D:\\Code\\Banking\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\Code\\Banking\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\Code\\Banking\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-08-05 15:39:41"}
{"level":"error","message":"Failed to parse email 198790694f976676: match is not defined","service":"banking-backend","stack":"ReferenceError: match is not defined\n    at EmailParser.extractAmount (D:\\Code\\Banking\\src\\services\\emailParser.js:218:13)\n    at EmailParser.parseTransactionEmail (D:\\Code\\Banking\\src\\services\\emailParser.js:112:27)\n    at GmailService.processMessage (D:\\Code\\Banking\\src\\services\\gmailService.js:348:49)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GmailService.checkNewEmails (D:\\Code\\Banking\\src\\services\\gmailService.js:273:11)\n    at async cron.schedule.scheduled [as _execution] (D:\\Code\\Banking\\src\\server.js:80:7)","timestamp":"2025-08-05 15:55:55"}
{"level":"error","message":"Failed to parse email 18829ad3a370248d: match is not defined","service":"banking-backend","stack":"ReferenceError: match is not defined\n    at EmailParser.extractAmount (D:\\Code\\Banking\\src\\services\\emailParser.js:218:13)\n    at EmailParser.parseTransactionEmail (D:\\Code\\Banking\\src\\services\\emailParser.js:112:27)\n    at GmailService.processMessage (D:\\Code\\Banking\\src\\services\\gmailService.js:348:49)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async GmailService.checkNewEmails (D:\\Code\\Banking\\src\\services\\gmailService.js:273:11)\n    at async cron.schedule.scheduled [as _execution] (D:\\Code\\Banking\\src\\server.js:80:7)","timestamp":"2025-08-05 15:55:56"}
{"level":"error","message":"Telegram bot connection test failed:","service":"banking-backend","timestamp":"2025-08-05 16:55:41"}
{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"currentRetryAttempt":0,"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"],"maxRetryDelay":****************,"noResponseRetries":2,"retry":3,"retryDelayMultiplier":2,"statusCodesToRetry":[[100,199],[408,408],[429,429],[500,599]],"timeOfFirstRequest":*************,"totalTimeout":****************},"url":"https://oauth2.googleapis.com/token"},"level":"error","message":"Gmail connection test failed: invalid_client","response":{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"]},"url":"https://oauth2.googleapis.com/token"},"data":{"error":"invalid_client","error_description":"Unauthorized"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","cache-control":"no-cache, no-store, max-age=0, must-revalidate","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Fri, 08 Aug 2025 20:19:11 GMT","expires":"Mon, 01 Jan 1990 00:00:00 GMT","pragma":"no-cache","server":"scaffolding on HTTPServer2","transfer-encoding":"chunked","vary":"Origin, X-Origin, Referer","x-content-type-options":"nosniff","x-frame-options":"SAMEORIGIN","x-xss-protection":"0"},"request":{"responseURL":"https://oauth2.googleapis.com/token"},"status":401,"statusText":"Unauthorized"},"service":"banking-backend","stack":"Error: invalid_client\n    at Gaxios._request (D:\\Code\\Banking\\node_modules\\gaxios\\build\\src\\gaxios.js:142:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async OAuth2Client.refreshTokenNoCache (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:212:19)\n    at async OAuth2Client.getRequestMetadataAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:333:17)\n    at async OAuth2Client.requestAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:418:23)\n    at async GmailService.testConnection (D:\\Code\\Banking\\src\\services\\gmailService.js:120:23)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:42:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:118:7)","status":401,"timestamp":"2025-08-09 03:19:12"}
{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"currentRetryAttempt":0,"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"],"maxRetryDelay":****************,"noResponseRetries":2,"retry":3,"retryDelayMultiplier":2,"statusCodesToRetry":[[100,199],[408,408],[429,429],[500,599]],"timeOfFirstRequest":*************,"totalTimeout":****************},"url":"https://oauth2.googleapis.com/token"},"level":"error","message":"Failed to initialize Gmail service: invalid_client","response":{"config":{"body":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","data":"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.","headers":{"Content-Type":"application/x-www-form-urlencoded","User-Agent":"google-api-nodejs-client/9.15.1","x-goog-api-client":"gl-node/22.13.0"},"method":"POST","responseType":"unknown","retry":true,"retryConfig":{"httpMethodsToRetry":["GET","PUT","POST","HEAD","OPTIONS","DELETE"]},"url":"https://oauth2.googleapis.com/token"},"data":{"error":"invalid_client","error_description":"Unauthorized"},"headers":{"alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000","cache-control":"no-cache, no-store, max-age=0, must-revalidate","content-encoding":"gzip","content-type":"application/json; charset=utf-8","date":"Fri, 08 Aug 2025 20:19:11 GMT","expires":"Mon, 01 Jan 1990 00:00:00 GMT","pragma":"no-cache","server":"scaffolding on HTTPServer2","transfer-encoding":"chunked","vary":"Origin, X-Origin, Referer","x-content-type-options":"nosniff","x-frame-options":"SAMEORIGIN","x-xss-protection":"0"},"request":{"responseURL":"https://oauth2.googleapis.com/token"},"status":401,"statusText":"Unauthorized"},"service":"banking-backend","stack":"Error: invalid_client\n    at Gaxios._request (D:\\Code\\Banking\\node_modules\\gaxios\\build\\src\\gaxios.js:142:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async OAuth2Client.refreshTokenNoCache (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:212:19)\n    at async OAuth2Client.getRequestMetadataAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:333:17)\n    at async OAuth2Client.requestAsync (D:\\Code\\Banking\\node_modules\\google-auth-library\\build\\src\\auth\\oauth2client.js:418:23)\n    at async GmailService.testConnection (D:\\Code\\Banking\\src\\services\\gmailService.js:120:23)\n    at async GmailService.initialize (D:\\Code\\Banking\\src\\services\\gmailService.js:42:7)\n    at async startServer (D:\\Code\\Banking\\src\\server.js:118:7)","status":401,"timestamp":"2025-08-09 03:19:12"}
